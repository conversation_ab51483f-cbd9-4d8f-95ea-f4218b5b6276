# Daftar Endpoint Backend

## User & Auth

- POST /register — Registrasi user baru
- POST /login — Login user
- POST /reset-password — Reset password user
- GET /users — Ambil semua user _(admin)_
- GET /users/:id — Ambil user berdasarkan ID _(admin/user sendiri)_
- GET /getregister — Ambil data user (tanpa password)

## Layanan

- POST /layanan — Tambah layanan _(admin)_
- GET /getlayanan — Ambil semua layanan
- GET /layanan/:id — Ambil layanan berdasarkan ID

## Pilih Layanan

- POST /pilihlayanan — Tambah pilihan layanan _(admin)_
- GET /getpilihlayanan — Ambil semua pilihan layanan

## Produk

- POST /tambahproduk — Tambah produk _(admin)_
- GET /getproduk — Ambil semua produk

## Produk Detail

- POST /tambahprodukdetail — Tambah produk detail _(admin)_
- GET /getprodukdetail — Ambil semua produk detail

## Produk Merek

- POST /tambahprodukmerek — Tambah produk merek _(admin)_
- GET /getprodukmerek — Ambil semua produk merek

## Pilih Dokter/Psikolog

- POST /tambahpilihdokterpsikolog — Tambah pilihan dokter/psikolog _(admin)_
- GET /getpilihdokterpsikolog — Ambil semua pilihan dokter/psikolog

## Durasi

- POST /tambahdurasi — Tambah durasi _(admin)_
- GET /getdurasi — Ambil semua durasi

## Booking

- POST /tambahbooking — Tambah booking
- GET /getbooking — Ambil semua booking (user: milik sendiri, admin: semua)

---

**Catatan:**

- Endpoint bertanda _(admin)_ sebaiknya hanya diakses oleh admin.
- Endpoint lain dapat diakses oleh user biasa.
- Silakan sesuaikan endpoint sesuai kebutuhan aplikasi Anda.

# 🎯 Demo Guide - PintuKeluar Frontend

## 🚀 Quick Demo Flow

### Step 1: Start Application
```bash
cd FrontEnd
npm run dev
```
Akses: http://localhost:5173/

### Step 2: Test Registration Flow
1. **Akses halaman registrasi**: http://localhost:5173/register
2. **Isi form dengan data baru**:
   - Name: <PERSON>
   - <PERSON>: <EMAIL>
   - Password: password123
   - Confirm Password: password123
3. **<PERSON>lik "Create Account"**
4. **Verify**: Success message muncul dan redirect ke login

### Step 3: Test Login dengan User Baru
1. **<PERSON> halaman login**, gunakan credentials yang baru dibuat:
   - Email: <EMAIL>
   - Password: password123
2. **Klik "Sign in"**
3. **Verify**: Redirect ke User Dashboard

### Step 4: Test User Dashboard
1. **Verify tampilan User Dashboard**:
   - Navbar menampilkan "Welcome, <PERSON>" dengan badge "User"
   - Statistics cards menampilkan data layanan dan booking
   - Available services list
   - My bookings section
2. **Test logout**: Klik tombol "Logout"
3. **Verify**: Redirect ke login page

### Step 5: Test Admin Login
1. **Login sebagai admin**:
   - Email: <EMAIL>
   - Password: password
2. **Verify**: Redirect ke Admin Dashboard

### Step 6: Test Admin Dashboard
1. **Verify tampilan Admin Dashboard**:
   - Navbar menampilkan "Welcome, Admin User" dengan badge "Admin"
   - Statistics cards menampilkan total users (sekarang 3), layanan, bookings, products
   - Recent users list (termasuk John Doe yang baru didaftarkan)
   - Quick action buttons
2. **Verify user baru muncul di list**: John Doe dengan role "user"

### Step 7: Test Route Protection
1. **Logout dari admin**
2. **Coba akses langsung**: http://localhost:5173/admin/dashboard
3. **Verify**: Redirect ke login
4. **Login sebagai user** (<EMAIL>)
5. **Coba akses**: http://localhost:5173/admin/dashboard
6. **Verify**: Redirect ke user dashboard (role protection)

## 🎨 UI/UX Features Demo

### Registration Form Features:
- ✅ Real-time form validation
- ✅ Password confirmation matching
- ✅ Email format validation
- ✅ Duplicate email detection
- ✅ Loading states
- ✅ Success/error messages
- ✅ Responsive design

### Login Form Features:
- ✅ Form validation
- ✅ Error handling
- ✅ Loading states
- ✅ Demo credentials display
- ✅ Link to registration

### Dashboard Features:
- ✅ Role-based content
- ✅ Statistics cards
- ✅ Data visualization
- ✅ Responsive layout
- ✅ Navigation with role badges
- ✅ Logout functionality

## 🧪 Advanced Testing Scenarios

### Test Error Handling:
1. **Registration dengan email yang sudah ada**:
   - Coba <NAME_EMAIL>
   - Verify error message: "Email already exists"

2. **Password tidak match**:
   - Password: test123
   - Confirm Password: test456
   - Verify error: "Passwords do not match"

3. **Password terlalu pendek**:
   - Password: 123
   - Verify error: "Password must be at least 6 characters"

### Test Form Validation:
1. **Empty fields**: Submit form kosong
2. **Invalid email**: test@invalid
3. **Name kosong**: Leave name field empty

### Test Navigation:
1. **Link antar halaman**: Login ↔ Register
2. **Protected routes**: Direct URL access
3. **Role-based redirects**: User trying to access admin

## 📱 Responsive Testing

### Mobile View:
1. **Buka DevTools** (F12)
2. **Toggle device toolbar** (Ctrl+Shift+M)
3. **Test di berbagai ukuran**:
   - iPhone SE (375px)
   - iPad (768px)
   - Desktop (1024px+)

### Verify:
- ✅ Forms tetap usable di mobile
- ✅ Navigation responsive
- ✅ Cards stack properly
- ✅ Text readable di semua ukuran

## 🔍 Developer Tools Testing

### Check localStorage:
```javascript
// Buka Console (F12)
localStorage.getItem('token')
localStorage.getItem('user')

// Clear session
localStorage.clear()
```

### Network Tab:
1. **Buka Network tab**
2. **Perform registration/login**
3. **Verify mock API calls**
4. **Check response data**

### Console Messages:
- No JavaScript errors
- Clean console output
- Proper state management

## 🎯 Demo Script untuk Presentasi

### Opening (2 menit):
"Ini adalah sistem autentikasi dan otorisasi lengkap untuk aplikasi PintuKeluar, dibangun dengan React.js dan dilengkapi dengan role-based access control."

### Registration Demo (3 menit):
1. "Mari kita mulai dengan membuat akun baru..."
2. Show form validation
3. Show success flow
4. "User baru otomatis mendapat role 'user'"

### Login & Dashboard Demo (5 menit):
1. "Login dengan user baru..."
2. Show user dashboard features
3. "Sekarang login sebagai admin..."
4. Show admin dashboard with more privileges
5. "Perhatikan perbedaan akses dan data yang ditampilkan"

### Security Demo (3 menit):
1. "Mari test keamanan sistem..."
2. Show route protection
3. Show role-based access
4. "User tidak bisa mengakses admin area"

### Closing (2 menit):
"Sistem ini siap untuk integrasi dengan backend real dan dapat dengan mudah dikustomisasi sesuai kebutuhan bisnis."

## 🚀 Production Readiness

### Features Ready:
- ✅ Complete authentication flow
- ✅ User registration
- ✅ Role-based authorization
- ✅ Protected routing
- ✅ Error handling
- ✅ Responsive design
- ✅ Loading states
- ✅ Form validation

### Next Steps:
1. **Backend Integration**: Replace mock API with real endpoints
2. **Email Verification**: Add email confirmation flow
3. **Password Reset**: Implement forgot password feature
4. **Profile Management**: Add user profile editing
5. **Advanced Roles**: Add more granular permissions

## 📞 Support

Jika ada pertanyaan atau issues:
1. Check console untuk error messages
2. Verify network requests di DevTools
3. Check localStorage untuk session data
4. Review documentation di README.md

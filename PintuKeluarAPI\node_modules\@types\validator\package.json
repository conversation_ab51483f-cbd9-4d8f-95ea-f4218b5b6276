{"name": "@types/validator", "version": "13.12.2", "description": "TypeScript definitions for validator", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/validator", "license": "MIT", "contributors": [{"name": "tgfjt", "githubUsername": "tgfjt", "url": "https://github.com/tgfjt"}, {"name": "<PERSON><PERSON>", "githubUsername": "ch<PERSON>u", "url": "https://github.com/chrootsu"}, {"name": "<PERSON><PERSON>", "githubUsername": "IOAyman", "url": "https://github.com/IOAyman"}, {"name": "<PERSON><PERSON>", "githubUsername": "louy", "url": "https://github.com/louy"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "dept<PERSON>", "url": "https://github.com/deptno"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/builtinnya"}, {"name": "<PERSON>", "githubUsername": "qqilihq", "url": "https://github.com/qqilihq"}, {"name": "<PERSON>", "githubUsername": "keatz55", "url": "https://github.com/keatz55"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "MunifTanjim", "url": "https://github.com/MunifTanjim"}, {"name": "<PERSON>", "githubUsername": "vlapo", "url": "https://github.com/vlapo"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "Mattewn99", "url": "https://github.com/Mattewn99"}, {"name": "<PERSON>", "githubUsername": "dcfreire", "url": "https://github.com/dcfreire"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/validator"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "1b1887c67d11cdfd7d264409b9b2646ee6cc5a9a148d5c5cc9923185000e7aac", "typeScriptVersion": "4.8"}
# Testing Guide - PintuKeluar Frontend

## Quick Start Testing

### 1. Jalankan Aplikasi
```bash
cd FrontEnd
npm run dev
```

### 2. Buka Browser
Akses: http://localhost:5173/

### 3. Test Credentials

#### Admin Login:
- **Email**: <EMAIL>
- **Password**: password
- **Expected**: Redirect ke Admin Dashboard

#### User Login:
- **Email**: <EMAIL>
- **Password**: password
- **Expected**: Redirect ke User Dashboard

## Test Scenarios

### ✅ Authentication Tests

1. **Valid Login Test**
   - Input: <EMAIL> / password
   - Expected: Success login, redirect to admin dashboard
   - Verify: Token stored in localStorage

2. **Invalid Login Test**
   - Input: <EMAIL> / wrongpassword
   - Expected: Error message displayed
   - Verify: No redirect, stays on login page

3. **Empty Fields Test**
   - Input: Leave email/password empty
   - Expected: HTML5 validation prevents submission

### ✅ Authorization Tests

1. **Admin Access Test**
   - Login as admin
   - Expected: Access to admin dashboard with full stats
   - Verify: Can see user list, system statistics

2. **User Access Test**
   - Login as user
   - Expected: Access to user dashboard with limited view
   - Verify: Can see personal bookings, available services

3. **Direct URL Access Test**
   - Without login, try: http://localhost:5173/admin/dashboard
   - Expected: Redirect to login page
   - After login as user, try admin URL
   - Expected: Redirect to user dashboard

### ✅ Route Protection Tests

1. **Unauthenticated Access**
   - Clear localStorage: `localStorage.clear()`
   - Try accessing any dashboard URL
   - Expected: Redirect to login

2. **Wrong Role Access**
   - Login as user
   - Try accessing: /admin/dashboard
   - Expected: Redirect to /user/dashboard

3. **Logout Test**
   - Login successfully
   - Click logout button
   - Expected: Redirect to login, localStorage cleared

### ✅ UI/UX Tests

1. **Loading States**
   - Observe loading spinner during login
   - Check dashboard loading states
   - Expected: Smooth loading experience

2. **Responsive Design**
   - Test on mobile viewport (F12 > Device toolbar)
   - Expected: Layout adapts properly

3. **Error Handling**
   - Try invalid login
   - Expected: Clear error message displayed

## Browser Developer Tools Testing

### Check localStorage:
```javascript
// In browser console
localStorage.getItem('token')
localStorage.getItem('user')
```

### Check Network Requests:
1. Open Network tab in DevTools
2. Perform login
3. Verify mock API calls are working

### Check Console:
- No JavaScript errors should appear
- Look for any warning messages

## Manual Testing Checklist

### Login Page:
- [ ] Form validation works
- [ ] Error messages display correctly
- [ ] Loading state shows during submission
- [ ] Success redirects to correct dashboard

### Admin Dashboard:
- [ ] Statistics cards show correct numbers
- [ ] User list displays properly
- [ ] Quick action buttons are visible
- [ ] Navbar shows admin role badge
- [ ] Logout button works

### User Dashboard:
- [ ] Service list displays
- [ ] Booking list shows (even if empty)
- [ ] Statistics are relevant to user
- [ ] Navbar shows user role badge
- [ ] Logout button works

### Navigation:
- [ ] Protected routes work correctly
- [ ] Role-based redirects function
- [ ] Unauthorized access is blocked
- [ ] Logout clears session properly

## Common Issues & Solutions

### Issue: "Cannot read properties of undefined"
**Solution**: Check if user data is properly loaded in AuthContext

### Issue: Infinite redirect loop
**Solution**: Check ProtectedRoute logic and role validation

### Issue: Styles not loading
**Solution**: Verify CSS classes are correctly applied

### Issue: Mock API not working
**Solution**: Check browser console for JavaScript errors

## Advanced Testing

### Test with Real Backend:
1. Start your backend server
2. Update API base URL in `src/services/api.js`
3. Replace mock API calls with real API calls
4. Test with actual database data

### Performance Testing:
1. Open DevTools Performance tab
2. Record page load and interactions
3. Check for any performance bottlenecks

### Accessibility Testing:
1. Use screen reader to test navigation
2. Check keyboard navigation (Tab key)
3. Verify color contrast and text readability

## Automated Testing (Future Enhancement)

Consider adding these test frameworks:
- **Jest** for unit testing
- **React Testing Library** for component testing
- **Cypress** for end-to-end testing

Example test structure:
```javascript
// Example unit test
describe('AuthContext', () => {
  test('should login successfully with valid credentials', async () => {
    // Test implementation
  });
});
```

## Reporting Issues

When reporting issues, include:
1. Browser and version
2. Steps to reproduce
3. Expected vs actual behavior
4. Console error messages
5. Screenshots if applicable

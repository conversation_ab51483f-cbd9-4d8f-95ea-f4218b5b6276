-- For new changelog see CHANGELOG.md

v1.2.0  --  2017.02.22
* Improve `columns`:
  * ANSI formatting instructions are not counted in calculation of column width
	* Support custom 'align' per column
* Improve documentation, document `colums` utility

v1.1.0  --  2015.10.13
* Add clc.slice and clc.getStrippedLength (thanks @StreetStrider)
* Add examples folder (thanks @ralphtheninja)

v1.0.0  --  2015.04.22
* General modularization and files reorganization. From now on each utility is placed in individual
  module, and can be required directly without others. Alternatively all modules are provided on
  index module
* Support style nesting (thanks @rentalhost)
* Rename `trim` to `strip`
* Improve `strip` (previously `trim`) regex by relying on ansi-regex package
* Improve resolution of window size
* Add `columns` utility
* Add `art` utility  (thanks @rentalhost)
* Add visual test  (thanks @rentalhost)
* Drop support for v0.8 node
* Update lint configuration

v0.3.3  --  2015.03.20
* Fix throbber tests
* Fix spelling of LICENSE
* Improve documentation
* Configure lint scripts

v0.3.2  --  2014.04.27
* Fix errorneous dependency version in package.json

v0.3.1  --  2014.04.27
* Update package.json to use latest 'tad' engine (fixes tests evaluation)

v0.3.0  --  2014.04.27
* Move out all main modules from lib folder
* Improve `throbber` utility, so it no longer relies on `clock/lib/interval` interface
  and it's easier to use on its own
* Update internals to use latest versions of dependencies
* Remove Makefile (it's cross environement package)

v0.2.3  --  2013.09.16
Add `blink`

v0.2.2  --  2013.02.20
* Fix trim, so it trims all kind of ANSI sequence codes correctly
* Do not rely on getWindowSize (it may not be available at all)
* Fix some xterm colors references in documentation
* Add Missing MIT licence

v0.2.1  --  2012.10.05
Maintanance (Use v0.2 of memoizee)

v0.2.0  --  2012.09.19
Major refactor of internal algorithm, addition of new functionalities

Changes:
* Bright variants are now set as individual colors (not as additional
  characteristics as it was in 0.1.x). We should do: clc.redBright instead of
  clc.red.bright
* Removed 'gray' color, clc.blackBright has same effect and should be used
  instead

New functionalities:
* Already prepared configurations can now be extended directly, e.g.:
  var error = clc.red;
  var majorError = error.bold;
* 256 xTerm color support via xterm(n) and bgXterm(n) functions
* Move around functions: clc.move, clc.moveTo, clc.bol, clc.up, clc.down, clc.right and clc.left
* clc.reset - Outputs string that clears the terminal
* clc.beep
* clc.width & clc.height - Terminal characteristics properties

v0.1.7  --  2012.06.13
* Update up to v0.8 branch of es5-extre
* package.json now in npm friendly format

v0.1.6  --  2012.01.22
* Update dependencies to latest versions
* Travis CI support
* More reliable tests for throbber

v0.1.5  --  2011.12.12
* Cleared npm warning for misnamed property in package.json

v0.1.4  --  2011.10.05
* Added bright color variants

v0.1.3  --  2011.08.08
* Added TAD test suite to devDependencies, configured test commands.
  Tests can be run with 'make test' or 'npm test'

v0.1.2  --  2011.08.08
* trim - for removing ANSI formatting from string
* throbber - displays throbber with preconfigured interval
* Compatibility with es5-ext v0.6
* Test with TAD

v0.1.1  --  2011.07.12
* Better documentation

v0.1.0  --  2011.07.11
* Initial version

# Troubleshooting Guide - PintuKeluar Frontend

## 🚨 Registration Issues

### Problem: "Registration failed" error

#### Quick Debug Steps:

1. **Open Browser Console** (F12 → Console tab)
2. **Try registration again** and check console messages
3. **Look for these console logs**:
   - "Form submitted with data: ..."
   - "Sending registration data: ..."
   - "AuthContext register called with: ..."
   - "Mock API register called with: ..."
   - "Registration result: ..."

#### Expected Console Output:
```
Form submitted with data: {name: "Test", email: "<EMAIL>", password: "password123", confirmPassword: "password123"}
Sending registration data: {name: "Test", email: "<EMAIL>", password: "password123"}
AuthContext register called with: {name: "Test", email: "<EMAIL>", password: "password123"}
Mock API register called with: {name: "Test", email: "<EMAIL>", password: "password123"}
New user created: {id: 3, name: "Test", email: "<EMAIL>", ...}
Total users now: 3
Mock API returning: {data: {message: "User registered successfully", user: {...}}}
Registration result: {success: true, data: {...}}
```

#### Common Issues & Solutions:

### Issue 1: Import Error
**Symptoms**: Console shows "Cannot resolve module" or import errors
**Solution**: 
```bash
# Restart dev server
npm run dev
```

### Issue 2: Mock API Not Found
**Symptoms**: Console shows "mockAuthAPI is not defined"
**Solution**: Check import in AuthContext.jsx:
```javascript
import { mockAuthAPI } from '../services/mockAuth';
```

### Issue 3: Email Already Exists
**Symptoms**: Error message "Email already exists"
**Solution**: Use different email or clear mock data:
```javascript
// In browser console
localStorage.clear()
// Then refresh page
```

### Issue 4: Form Validation Error
**Symptoms**: Form doesn't submit, validation error shows
**Solution**: Check form data:
- Name: Not empty
- Email: Valid format
- Password: At least 6 characters
- Confirm Password: Matches password

## 🔧 Manual Testing Steps

### Step 1: Clear Browser Data
```javascript
// In browser console (F12)
localStorage.clear()
sessionStorage.clear()
location.reload()
```

### Step 2: Test with Fresh Data
Use these test credentials:
```
Name: John Doe
Email: <EMAIL>
Password: password123
Confirm Password: password123
```

### Step 3: Check Network Tab
1. Open DevTools (F12)
2. Go to Network tab
3. Submit form
4. Look for any failed requests

### Step 4: Verify Mock API
Test mock API directly in console:
```javascript
// In browser console
import('./src/services/mockAuth.js').then(module => {
  const { mockAuthAPI } = module;
  mockAuthAPI.register({
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123'
  }).then(result => {
    console.log('Direct API test result:', result);
  }).catch(error => {
    console.error('Direct API test error:', error);
  });
});
```

## 🐛 Common Error Messages

### "Registration failed"
- **Cause**: Generic error from AuthContext
- **Debug**: Check console for detailed error messages
- **Fix**: Look at specific error in console logs

### "Email already exists"
- **Cause**: Email is already in mock database
- **Fix**: Use different email or clear localStorage

### "Passwords do not match"
- **Cause**: Password and confirm password fields don't match
- **Fix**: Ensure both password fields have same value

### "Password must be at least 6 characters"
- **Cause**: Password too short
- **Fix**: Use password with 6+ characters

### "Name is required" / "Email is required"
- **Cause**: Required fields are empty
- **Fix**: Fill all required fields

## 🔍 Advanced Debugging

### Check AuthContext State
```javascript
// Add to Register.jsx temporarily
console.log('Auth context:', { register, user, isAuthenticated });
```

### Check Mock Users Array
```javascript
// In browser console
// This will show current users in mock database
console.log('Current mock users:', JSON.parse(localStorage.getItem('mockUsers') || '[]'));
```

### Verify Component Mounting
```javascript
// Add to Register.jsx useEffect
useEffect(() => {
  console.log('Register component mounted');
  console.log('Register function available:', typeof register);
}, []);
```

## 🚀 Quick Fixes

### Fix 1: Restart Development Server
```bash
# Stop current server (Ctrl+C)
# Then restart
npm run dev
```

### Fix 2: Clear All Browser Data
```javascript
// In browser console
localStorage.clear();
sessionStorage.clear();
// Then refresh page (F5)
```

### Fix 3: Use Different Email
Try with completely new email:
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Fix 4: Check Browser Compatibility
- Use Chrome/Firefox/Edge (latest versions)
- Disable browser extensions temporarily
- Try incognito/private mode

## 📞 If Still Not Working

### Collect Debug Information:
1. **Browser**: Chrome/Firefox/Safari + version
2. **Console Errors**: Copy all error messages
3. **Network Tab**: Any failed requests
4. **Form Data**: What data was submitted
5. **Expected vs Actual**: What should happen vs what happens

### Alternative Test Method:
Try login with existing users first:
- <EMAIL> / password
- <EMAIL> / password

If login works but registration doesn't, the issue is specifically with registration flow.

### Last Resort - Bypass Mock API:
Temporarily modify AuthContext to return success:
```javascript
const register = async (userData) => {
  // Temporary bypass for testing
  return { success: true, data: { message: 'Test success' } };
};
```

This will help isolate if the issue is with mock API or form handling.

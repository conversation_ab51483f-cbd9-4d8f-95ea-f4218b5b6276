-- DATABASE: pintu<PERSON><PERSON><PERSON>
CREATE DATABASE IF NOT EXISTS pintukeluar;
USE pintukeluar;

-- USERS
CREATE TABLE IF NOT EXISTS Users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('user', 'admin') NOT NULL DEFAULT 'user',
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);

-- LAYANANS
CREATE TABLE IF NOT EXISTS Layanans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nama_layanan VARCHAR(255) NOT NULL,
  userId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- <PERSON><PERSON><PERSON>HLAYANANS
CREATE TABLE IF NOT EXISTS pilihlayanans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nama_pilih_layanan VARCHAR(255),
  userId INT NOT NULL,
  layananId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- PRODUKS
CREATE TABLE IF NOT EXISTS produks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nama_produk VARCHAR(255),
  userId INT NOT NULL,
  layananId INT NOT NULL,
  pilihlayananId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (pilihlayananId) REFERENCES pilihlayanans(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- PRODUKDETAILS
CREATE TABLE IF NOT EXISTS produkdetails (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nama_produk_detail VARCHAR(255),
  userId INT NOT NULL,
  layananId INT NOT NULL,
  pilihlayananId INT NOT NULL,
  produkId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (pilihlayananId) REFERENCES pilihlayanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (produkId) REFERENCES produks(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- MEREKPRODUKS
CREATE TABLE IF NOT EXISTS merekproduks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nama_produk_merek VARCHAR(255),
  userId INT NOT NULL,
  layananId INT NOT NULL,
  pilihlayananId INT NOT NULL,
  produkId INT NOT NULL,
  produkdetailId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (pilihlayananId) REFERENCES pilihlayanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (produkId) REFERENCES produks(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (produkdetailId) REFERENCES produkdetails(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- DOKTERPSIKOLOGS
CREATE TABLE IF NOT EXISTS dokterPsikologs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  pilih_dokter_psikolog VARCHAR(255),
  userId INT NOT NULL,
  layananId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- DURASIS
CREATE TABLE IF NOT EXISTS durasis (
  id INT AUTO_INCREMENT PRIMARY KEY,
  durasi INT,
  userId INT NOT NULL,
  layananId INT NOT NULL,
  dokterpsikologId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (dokterpsikologId) REFERENCES dokterPsikologs(id) ON UPDATE CASCADE ON DELETE CASCADE
);

-- BOOKINGS
CREATE TABLE IF NOT EXISTS Bookings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  jam_booking TIME,
  userId INT NOT NULL,
  layananId INT NOT NULL,
  dokterpsikologId INT NOT NULL,
  durasiId INT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (userId) REFERENCES Users(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (layananId) REFERENCES Layanans(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (dokterpsikologId) REFERENCES dokterPsikologs(id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (durasiId) REFERENCES durasis(id) ON UPDATE CASCADE ON DELETE CASCADE
);

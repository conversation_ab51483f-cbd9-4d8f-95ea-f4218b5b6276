// Test file untuk mock API
import { mockAuthAPI } from './services/mockAuth.js';

// Test function
async function testMockAPI() {
  console.log('Testing mock API...');
  
  try {
    // Test register
    const registerResult = await mockAuthAPI.register({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('Register result:', registerResult);
    
    // Test login
    const loginResult = await mockAuthAPI.login({
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('Login result:', loginResult);
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run test
testMockAPI();

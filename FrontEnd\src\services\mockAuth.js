// Mock authentication service untuk testing
// Simulasi backend API responses

const mockUsers = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'password',
    role: 'admin',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    name: 'Regular User',
    email: '<EMAIL>',
    password: 'password',
    role: 'user',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

const mockLayanan = [
  {
    id: 1,
    nama_layanan: 'Konsultasi Psikologi',
    userId: 1,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    nama_layanan: '<PERSON><PERSON><PERSON>',
    userId: 1,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

const mockBookings = [
  {
    id: 1,
    jam_booking: '10:00:00',
    userId: 2,
    layananId: 1,
    dokterpsikologId: 1,
    durasiId: 1,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const mockAuthAPI = {
  login: async (credentials) => {
    await delay(1000); // Simulate network delay
    
    const user = mockUsers.find(u => 
      u.email === credentials.email && u.password === credentials.password
    );
    
    if (user) {
      const { password, ...userWithoutPassword } = user;
      return {
        data: {
          token: `mock-jwt-token-${user.id}`,
          user: userWithoutPassword
        }
      };
    } else {
      throw {
        response: {
          data: {
            message: 'Invalid email or password'
          }
        }
      };
    }
  },

  getUsers: async () => {
    await delay(500);
    const usersWithoutPasswords = mockUsers.map(({ password, ...user }) => user);
    return { data: usersWithoutPasswords };
  }
};

export const mockLayananAPI = {
  getAll: async () => {
    await delay(500);
    return { data: mockLayanan };
  }
};

export const mockBookingAPI = {
  getAll: async () => {
    await delay(500);
    return { data: mockBookings };
  }
};

export const mockProdukAPI = {
  getAll: async () => {
    await delay(500);
    return { data: [] }; // Empty for demo
  }
};

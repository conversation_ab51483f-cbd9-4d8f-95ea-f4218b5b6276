/* Basic CSS Reset and Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
}

/* Utility Classes */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-white { background-color: #ffffff; }
.bg-indigo-600 { background-color: #4f46e5; }
.bg-indigo-500 { background-color: #6366f1; }
.bg-indigo-700 { background-color: #3730a3; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-green-500 { background-color: #10b981; }
.bg-yellow-500 { background-color: #f59e0b; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-400 { background-color: #f87171; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-green-100 { background-color: #dcfce7; }
.text-white { color: #ffffff; }
.text-gray-900 { color: #111827; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.text-indigo-600 { color: #4f46e5; }
.text-red-700 { color: #b91c1c; }
.text-red-800 { color: #991b1b; }
.text-blue-800 { color: #1e40af; }
.text-green-800 { color: #166534; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.p-5 { padding: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-1 { margin-top: 0.25rem; }
.mb-8 { margin-bottom: 2rem; }
.ml-5 { margin-left: 1.25rem; }
.ml-2 { margin-left: 0.5rem; }
.w-full { width: 100%; }
.w-8 { width: 2rem; }
.w-12 { width: 3rem; }
.w-32 { width: 8rem; }
.w-0 { width: 0; }
.h-16 { height: 4rem; }
.h-8 { height: 2rem; }
.h-12 { height: 3rem; }
.h-32 { height: 8rem; }
.h-64 { height: 16rem; }
.max-w-md { max-width: 28rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-2xl { max-width: 42rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.space-y-8 > * + * { margin-top: 2rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }
.rounded-t-md { border-top-left-radius: 0.375rem; border-top-right-radius: 0.375rem; }
.rounded-b-md { border-bottom-left-radius: 0.375rem; border-bottom-right-radius: 0.375rem; }
.border { border-width: 1px; }
.border-transparent { border-color: transparent; }
.border-gray-300 { border-color: #d1d5db; }
.border-red-400 { border-color: #f87171; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.font-extrabold { font-weight: 800; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.text-center { text-align: center; }
.leading-6 { line-height: 1.5rem; }
.overflow-hidden { overflow: hidden; }
.relative { position: relative; }
.block { display: block; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-6 { gap: 1.5rem; }
.gap-4 { gap: 1rem; }
.divide-y > * + * { border-top-width: 1px; }
.divide-gray-200 > * + * { border-top-color: #e5e7eb; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-1 { flex: 1 1 0%; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border-width: 0; }
.appearance-none { appearance: none; }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-indigo-500:focus { --tw-ring-color: #6366f1; }
.focus\:border-indigo-500:focus { border-color: #6366f1; }
.focus\:z-10:focus { z-index: 10; }
.hover\:bg-indigo-700:hover { background-color: #3730a3; }
.hover\:bg-green-700:hover { background-color: #047857; }
.hover\:bg-yellow-700:hover { background-color: #a16207; }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-150 { transition-duration: 150ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
.animate-spin { animation: spin 1s linear infinite; }

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:px-0 { padding-left: 0; padding-right: 0; }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .sm\:rounded-md { border-radius: 0.375rem; }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}
